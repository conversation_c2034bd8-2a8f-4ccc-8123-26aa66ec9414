// import 'package:get/get.dart';
// import 'package:silverleaf/service/sample.dart';

// class SampleController extends GetxController {
//   var clint = SampleData();

//   fetchData() async {
//     try {
//       var data = await clint.homescreenService();
//       if (data != null) {
//         print(data);
//       } else {
//         print("Controller Error");
//       }
//     } catch (e) {
//       return e;
//     }
//   }
// }
