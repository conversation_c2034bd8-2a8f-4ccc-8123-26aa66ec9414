import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class StudentLeaveController extends GetxController {
  var counter = 1;
  String base_url = '';

  savestudent(
    leaveStatus,
    description,
    fromdate,
    todate,
    id,
    year_id,
    class_id,
    branch_id,
    section_id,
  ) async {
    final url =
        'https://silverleafms.in/silvar_leaf/api/leave/add-leave'; // Replace with the actual URL of your PHP script.
    final data = {
      'from_date': todate,
      'leave_type': leaveStatus,
      'to_date': fromdate,
      'description': description,
      'student_id': id,
      'year_id': year_id,
      'class_id': class_id,
      'section_id': section_id,
      'branch_id': branch_id,
    };
    print(data);
    final response = await http.post(Uri.parse(url), body: data);

    final Map<String, dynamic> result = json.decode(response.body);

    print(result);

    return result;
  }

  Auth(String mobile) async {
    final url = 'https://silverleafms.in/silvar_leaf/api/students/auth';
    final data = {'mobile': mobile};

    final response = await http.post(Uri.parse(url), body: data);

    final result = json.decode(response.body);
    // print(result['data']);

    return result;
  }
}
