// import 'dart:convert';
// import 'dart:developer';

// import 'package:http/http.dart' as http;

// class SampleData {
//   Future homescreenService() async {
//     // SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
//     // var token = sharedPreferences.getString(Constants.authToken);
//     try {
//       var response = await http.get(
//         Uri.parse(
//             'https://silverleafms.in/silvar_leaf/api/masters/branch'),
//       );

//       var jsonresponse = jsonDecode(response.body);

//       if (response.statusCode == 200 || response.statusCode == 201) {
//         if (jsonresponse['status'] == 'success') {
//           log('kkkkk');
//           log(response.body);
//           // log(jsonresponse);
//           // return HomeModel.fromJson(jsonresponse)
//         } else {
//           log(response.statusCode.toString());
//         }
//       } else {
//         log("Error");
//       }
//     } catch (e) {
//       rethrow;
//     }
//   }
// }
