// ignore_for_file: prefer_const_constructors

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';

class MainPageChat extends StatefulWidget {
  final String staff_id;
  final String student_id;
  final String class_name;
  final String Section_name;
  final String Profile;
  final String name;
  final String sender_id;
  final String class_id;
  final String section_id;
  final String branch_id;
  final int message_count;

  const MainPageChat({
    super.key,
    required this.staff_id,
    required this.student_id,
    required this.Section_name,
    required this.class_name,
    required this.Profile,
    required this.name,
    required this.sender_id,
    required this.class_id,
    required this.section_id,
    required this.branch_id,
    required this.message_count,
  });

  @override
  State<MainPageChat> createState() => _MainPageChatState();
}

class _MainPageChatState extends State<MainPageChat> {
  TextEditingController sendMessage = TextEditingController();
  var jsonData;
  late bool _isFetchingData;
  List<Message> message = [];
  List<Message> chat = [];

  List userNames = [];
  List<Message> sortedMessages = [];
  final StreamController<List<Message>> _messageStreamController =
      StreamController<List<Message>>();
  late ScrollController _scrollController;
  late Timer _timer;
  bool isLoading = true;

  Future<List> list_message() async {
    final url;
    final data;
    var type;

    url = 'https://silverleafms.in/silvar_leaf/api/staffs/get-message';
    data = {
      'staff_id': widget.staff_id,
      'student_id': widget.student_id,
      'sender_id': widget.staff_id,
    };

    final response = await http.post(Uri.parse(url), body: data);

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);

      var response_student = responseBody['data'];

      List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
        responseBody['data'],
      );

      jsonData.map((data) {
        final msg = Message(
          date:
              data['posted_date'] == null
                  ? DateTime.now()
                  : DateTime.parse(data['posted_date']),
          isSentByMe: data['isSentByMe'],
          text: data['message'],
        );
        setState(() {
          data.length == 0 ? null : chat.add(msg);
        });
      }).toList();

      return response_student;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<List<Message>> get_message() async {
    final url = 'https://silverleafms.in/silvar_leaf/api/staffs/get-message';
    final data = {
      'staff_id': widget.staff_id,
      'student_id': widget.student_id,
      'sender_id': widget.student_id,
    };

    final response = await http.post(Uri.parse(url), body: data);

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      var response_student = responseBody['data'];

      List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
        responseBody['data'],
      );

      List<Message> messages =
          jsonData.map((data) {
            return Message(
              date: DateTime.parse(data['posted_date']),
              isSentByMe: data['isSentByMe'],
              text: data['message'],
            );
          }).toList();

      // Add messages to the stream controller
      _messageStreamController.add(messages);

      return messages;
    } else {
      throw Exception('Failed to load data');
    }
  }

  save_message() async {
    final url;
    final data;
    var type;
    //final msg = Message(date: DateTime.now(), isSentByMe: int.parse(widget.sender_id), text: sendMessage.text);
    url = 'https://silverleafms.in/silvar_leaf/api/staffs/send-message-teacher';
    data = {
      'message': sendMessage.text,
      'staff_id': widget.staff_id,
      'student_id': widget.student_id,
      'isSentByMe': widget.sender_id,
      'class_id': widget.class_id,
      'section_id': widget.section_id,
      'branch_id': widget.branch_id,
      'posted_date': DateTime.now().toString(),
      'message_status_student': '1',
      'message_status_teacher': '1',
    };

    final response = await http.post(Uri.parse(url), body: data);
    print(response.body);
    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      print(responseBody);
      var response_student = responseBody['data'];
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
        } else if (userNames.last == '2') {
          setState(() {
            isTeacher = true;
          });
        } else {}
      });
    }
  }

  void initState() {
    super.initState();
    _isFetchingData = true;
    //  this.get_message();
    this.getListString();
    _scrollController = ScrollController();
    _timer = Timer.periodic(Duration(seconds: 5), (_) {
      get_message();
    });
    Future.delayed(Duration(seconds: 5), () {
      setState(() {
        isLoading = false;
      });
    });
    list_message();
  }

  void dispose() {
    // fetchData();
    _isFetchingData = false;
    super.dispose();
  }

  String formatDate(DateTime date) {
    DateTime today = DateTime.now();
    DateTime yesterday = today.subtract(Duration(days: 1));

    if (DateFormat.yMMMd().format(date) == DateFormat.yMMMd().format(today)) {
      return 'Today';
    } else if (DateFormat.yMMMd().format(date) ==
        DateFormat.yMMMd().format(yesterday)) {
      return 'Yesterday';
    } else {
      return DateFormat.yMMMd().format(date);
    }
  }

  Future<bool> subback() async {
    //  await Get.offAll(() => MainBoard());

    globalBottomBarIndex = 3;
    // });

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => MainBoard()),
    );

    return Future.value(true);
  }

  Widget buildMessageItem(message) {
    DateTime dateTime;
    DateFormat inputFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    dateTime = inputFormat.parse(message.date.toString());
    String formattedTime = DateFormat('hh:mm a').format(dateTime);

    if (message.isSentByMe.toString() != widget.sender_id) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Visibility(
            visible: message.isSentByMe == widget.sender_id ? false : true,
            child: Padding(
              padding: EdgeInsets.only(left: 15.0),
              child: CircleAvatar(
                backgroundImage: NetworkImage(
                  message.isSentByMe.toString() == widget.sender_id
                      ? userNames[8]
                      : widget.Profile,
                ),
              ),
            ),
          ),
          SizedBox(width: 2.0.wp),
          Flexible(
            child: Card(
              color: Colors.white,
              borderOnForeground: true,
              elevation: 8,
              child: Padding(
                padding: EdgeInsets.all(12),
                child: Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: message.text, // The main message text
                        style: TextStyle(
                          fontSize: 15, // Small font size for the main text
                        ),
                      ),
                      TextSpan(
                        text: '\n$formattedTime', // The formatted time text
                        style: TextStyle(
                          fontSize: 8, // Smaller font size for the time
                          color: Colors.grey, // Grey color for the time text
                        ),
                      ),
                    ],
                  ),
                  overflow: TextOverflow.visible,
                  softWrap: true,
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Flexible(
            child: Card(
              color: Colors.white,
              borderOnForeground: true,
              elevation: 8,
              child: Padding(
                padding: EdgeInsets.all(12),
                child: Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: message.text, // The main message text
                        style: TextStyle(
                          fontSize: 15, // Small font size for the main text
                        ),
                      ),
                      TextSpan(
                        text: '\n$formattedTime', // The formatted time text
                        style: TextStyle(
                          fontSize: 8, // Smaller font size for the time
                          color: Colors.grey, // Grey color for the time text
                        ),
                      ),
                    ],
                  ),
                  overflow: TextOverflow.visible,
                  softWrap: true,
                ),
              ),
            ),
          ),
          SizedBox(width: 2.0.wp),
          Visibility(
            visible: message.isSentByMe == widget.sender_id ? false : true,
            child: Padding(
              padding: EdgeInsets.only(right: 15.0),
              child: CircleAvatar(
                backgroundImage: NetworkImage(
                  message.isSentByMe.toString() == widget.sender_id
                      ? userNames[8]
                      : widget.Profile,
                ),
              ),
            ),
          ),
        ],
      );
    }
  }

  var class_display;
  @override
  Widget build(BuildContext context) {
    if (widget.class_name != '') {
      class_display = 'Std- ${widget.class_name}, ${widget.Section_name}';
    } else {
      class_display = '';
    }

    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 8.0.hp),
          child: CustomizedAppBar(
            back: subback,
            profile: () {},
            screenName: 'Message',
            screen_id: 2,
          ),
        ),
        body: WillPopScope(
          onWillPop: subback,
          child: Column(
            children: [
              Stack(
                children: [
                  Container(
                    width: double.infinity,
                    height: 13.0.hp,
                    color: appcolor.withOpacity(.3),
                  ),
                  Container(
                    width: double.infinity,
                    height: 13.0.hp,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(20.0.sp),
                        bottomRight: Radius.circular(20.0.sp),
                      ),
                      color: Colors.white,
                    ),
                    child: Padding(
                      padding: EdgeInsets.only(left: 15.0.sp),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 25.0.sp,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(80.0),
                              child: Image.network(
                                widget.Profile,
                                width: 160,
                                height: 160,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Image.asset(
                                    'images/user.png',
                                    width: 160,
                                    height: 160,
                                  );
                                },
                              ),
                            ),
                          ),
                          SizedBox(width: 4.0.wp),
                          Text(
                            "${widget.name}, ${class_display}",
                            style: textStyle.copyWith(
                              fontSize: 12.0.sp,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: StreamBuilder<List<Message>>(
                  stream: _messageStreamController.stream,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      List<Message> message = snapshot.data!;
                      // WidgetsBinding.instance!.addPostFrameCallback((_) {
                      //   Future.delayed(Duration(milliseconds: 100), () {
                      //     _scrollController.animateTo(
                      //       _scrollController.position.maxScrollExtent,
                      //       duration: Duration(milliseconds: 500),
                      //       curve: Curves.easeInOut,
                      //     );
                      //   });
                      // });

                      return Column(
                        children: [
                          Expanded(
                            child: Container(
                              color:
                                  message.length == 0
                                      ? Colors.white
                                      : appcolor.withOpacity(.3),
                              child: ListView.builder(
                                controller: _scrollController,
                                itemCount: message.length,
                                itemBuilder: (context, index) {
                                  final msg = message[index];
                                  print("message screen");
                                  print(msg);
                                  // Check if this message's date is different from the previous one
                                  bool showDateHeader =
                                      index == 0 ||
                                      msg.date.day !=
                                          message[index - 1].date.day ||
                                      msg.date.month !=
                                          message[index - 1].date.month ||
                                      msg.date.year !=
                                          message[index - 1].date.year;

                                  if (showDateHeader) {
                                    return Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          height: 100,
                                          child: Center(
                                            child: Container(
                                              color: subappcolor,
                                              alignment: Alignment.center,
                                              height: 4.0.hp,
                                              width: 30.0.wp,
                                              child: Text(
                                                formatDate(msg.date),
                                                style: TextStyle(
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        buildMessageItem(msg),
                                      ],
                                    );
                                  } else {
                                    // Just display the message item
                                    return buildMessageItem(msg);
                                  }
                                },
                              ),
                            ),
                          ),
                          Container(
                            color: Colors.grey.shade300,
                            child: TextField(
                              controller: sendMessage,
                              onSubmitted: (value) {
                                final msg = Message(
                                  date: DateTime.now(),
                                  isSentByMe: int.parse(widget.sender_id),
                                  text: value,
                                );
                                setState(() {
                                  value.isNotEmpty ? message.add(msg) : null;
                                  save_message();
                                  sendMessage.clear();
                                });
                              },
                              decoration: InputDecoration(
                                suffixIcon: IconButton(
                                  icon: Icon(Icons.send),
                                  onPressed: () {
                                    final msg = Message(
                                      date: DateTime.now(),
                                      isSentByMe: int.parse(widget.sender_id),
                                      text: sendMessage.text,
                                    );
                                    setState(() {
                                      sendMessage.text.isNotEmpty
                                          ? message.add(msg)
                                          : null;
                                      save_message();
                                      sendMessage.clear();
                                      _scrollController.animateTo(
                                        _scrollController
                                            .position
                                            .maxScrollExtent,
                                        duration: Duration(milliseconds: 300),
                                        curve: Curves.easeOut,
                                      );
                                    });
                                  },
                                ),
                                contentPadding: EdgeInsets.all(12),
                                hintText: "Type your message here..",
                              ),
                            ),
                          ),
                        ],
                      );
                    } else if (snapshot.hasError) {
                      return Center(
                        child: Column(children: [CircularProgressIndicator()]),
                      );
                      ;
                    } else {
                      return Column(
                        children: [
                          Expanded(
                            child: Container(
                              color: appcolor.withOpacity(.3),
                              child: ListView.builder(
                                itemCount: chat.length,
                                itemBuilder: (context, index) {
                                  final msg = chat[index];
                                  // Check if this message's date is different from the previous one
                                  bool showDateHeader =
                                      index == 0 ||
                                      msg.date.day !=
                                          chat[index - 1].date.day ||
                                      msg.date.month !=
                                          chat[index - 1].date.month ||
                                      msg.date.year !=
                                          chat[index - 1].date.year;

                                  if (showDateHeader) {
                                    return Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          height: 100,
                                          child: Center(
                                            child: Container(
                                              color: subappcolor,
                                              alignment: Alignment.center,
                                              height: 4.0.hp,
                                              width: 30.0.wp,
                                              child: Text(
                                                formatDate(msg.date),
                                                style: TextStyle(
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        buildMessageItem(msg),
                                      ],
                                    );
                                  } else {
                                    // Just display the message item
                                    return buildMessageItem(msg);
                                  }
                                },
                              ),
                            ),
                          ),
                          Container(
                            color: Colors.grey.shade300,
                            child: TextField(
                              controller: sendMessage,
                              onSubmitted: (value) {
                                final msg = Message(
                                  date: DateTime.now(),
                                  isSentByMe: int.parse(widget.sender_id),
                                  text: value,
                                );
                                setState(() {
                                  value.isNotEmpty ? message.add(msg) : null;
                                  save_message();
                                  sendMessage.clear();
                                });
                              },
                              decoration: InputDecoration(
                                suffixIcon: IconButton(
                                  icon: Icon(Icons.send),
                                  onPressed: () {
                                    final msg = Message(
                                      date: DateTime.now(),
                                      isSentByMe: int.parse(widget.sender_id),
                                      text: sendMessage.text,
                                    );
                                    setState(() {
                                      sendMessage.text.isNotEmpty
                                          ? message.add(msg)
                                          : null;
                                      save_message();
                                      sendMessage.clear();
                                      _scrollController.animateTo(
                                        _scrollController
                                            .position
                                            .maxScrollExtent,
                                        duration: Duration(milliseconds: 300),
                                        curve: Curves.easeOut,
                                      );
                                    });
                                  },
                                ),
                                contentPadding: EdgeInsets.all(12),
                                hintText: "Type your message here..",
                              ),
                            ),
                          ),
                        ],
                      );
                    }
                  },
                  // },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Message {
  final String text;
  final DateTime date;
  final int isSentByMe;
  const Message({
    required this.date,
    required this.isSentByMe,
    required this.text,
  });
}
