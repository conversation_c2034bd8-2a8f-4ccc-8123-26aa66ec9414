// ignore_for_file: prefer_const_constructors, avoid_unnecessary_containers

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/api/firebase_api.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';

class ProfileScreen extends StatefulWidget {
  final String student_profile;
  final String student_name;
  final String student_class;
  final String student_section;
  final String student_branch;
  final String student_blood_group;
  final String student_father_name;
  final String student_mother_name;
  final String student_primary_contact_no;
  final String student_secondary_contact_no;
  final String id;
  const ProfileScreen({
    super.key,
    required this.student_profile,
    required this.student_name,
    required this.student_class,
    required this.student_section,
    required this.student_branch,
    required this.student_blood_group,
    required this.student_father_name,
    required this.student_mother_name,
    required this.student_primary_contact_no,
    required this.student_secondary_contact_no,
    required this.id,
  });

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String blood_group = '';
  String student_name = '';
  String student_class = '';
  String student_section = '';
  String student_branch = '';
  String student_blood_group = '';
  String student_father_name = '';
  String student_mother_name = '';
  String student_primary_contact_no = '';
  String student_secondary_contact_no = '';
  List subTitle = [];
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 10.0.hp),
          child: CustomizedAppBar(
            back: () {
              Get.back();
            },
            profile: () {},
            screenName: "Profile",
            screen_id: 2,
          ),
        ),
        backgroundColor: Colors.white,
        body: SizedBox(
          child: Column(children: [header(), Expanded(child: detailsScreen())]),
        ),
      ),
    );
  }

  void initState() {
    super.initState();

    print('primary number');
    print(widget.student_primary_contact_no);
    blood_group = widget.student_blood_group;

    //fetchstudent();

    subTitle.add(blood_group);
    subTitle.add(widget.student_father_name);
    subTitle.add(widget.student_mother_name);
    subTitle.add(widget.student_primary_contact_no);
    subTitle.add(widget.student_secondary_contact_no);
    fetchstudent();
  }

  List title = [
    'Blood Group',
    'Father’s Name',
    'Mother’s Name',
    'Primary Contact Number',
    'Seconday Contact Number',
  ];
  List<ProfileObject> student_data_profile = [];
  fetchstudent() async {
    int currentYear = DateTime.now().year;
    int month = DateTime.now().month;

    final url =
        'https://silverleafms.in/silvar_leaf/api/students/profile-student-list';
    final data = {
      'mobile': widget.student_primary_contact_no,
      'secondary': widget.student_secondary_contact_no,
    };

    final response = await http.post(Uri.parse(url), body: data);
    if (response.statusCode == 200) {
      final result = json.decode(response.body);

      List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
        result['data'],
      );

      List<dynamic> dataList = result['data'];

      List<dynamic> filteredData =
          dataList.where((item) {
            if ((int.parse(item['academic_year'].toString().split('-')[0]) ==
                    currentYear) &&
                month >= 5) {
              return true;
            } else if ((int.parse(
                      item['academic_year'].toString().split('-')[1],
                    ) ==
                    currentYear) &&
                month <= 4) {
              return true;
            } else {
              return false;
            }
          }).toList();

      setState(() {
        student_data_profile.clear();

        if (filteredData.length > 0) {
          student_data_profile =
              filteredData
                  .map(
                    (result) => ProfileObject(
                      id: result['id'].toString(),
                      name: result['name'],
                      className: result['class_name'],
                      branchName: result['branch_name'],
                      sectionName: result['section_name'],
                      sectionId: result['section_id'].toString(),
                      classId: result['class_id'].toString(),
                      branchId: result['branch_id'].toString(),
                      type: result['type'].toString(),
                      profile: result['profile'],
                      bloodGroup: result['blood_group_name'],
                      fatherName: result['father_name'],
                      motherName: result['mother_name'],
                      primaryNumber: result['primary_number'].toString(),
                      secondaryNumber: result['secondary_number'].toString(),
                      academic_id: result['academic_id'].toString(),
                    ),
                  )
                  .toList();
        }
      });

      // print(student_data_profile.length);

      //return result['data'];
    } else {
      throw Exception('Failed to load data');
    }
  }

  List titleteacher = [
    'Blood Group',
    'Father’s Name',
    'Mother’s Name',
    'Marital Status',
    'Qualification',
    'Mobile Number',
  ];
  List subTitleteacher = [
    'A1 +ve',
    'B. Praveen',
    'Mohana.P',
    'Single',
    'B.Com',
    '+91 87600 19297',
  ];
  Widget detailsScreen() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Existing ListView.separated
        ListView.separated(
          separatorBuilder: (context, index) {
            return Divider();
          },
          itemCount: isTeacher == false ? title.length : titleteacher.length,
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return Container(
              margin: EdgeInsets.only(left: 25.0.sp),
              padding: EdgeInsets.all(5.0.sp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isTeacher == false ? title[index] : titleteacher[index],
                    style: textStyle.copyWith(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.0.hp),
                  Text(
                    isTeacher == false
                        ? subTitle[index]
                        : subTitleteacher[index],
                    style: textStyle.copyWith(
                      fontWeight: FontWeight.w300,
                      fontSize: 12.0.sp,
                    ),
                  ),
                  SizedBox(height: 1.0.hp),
                ],
              ),
            );
          },
        ),

        SizedBox(height: 20.0.sp),
      ],
    );
  }

  static Future logout() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.remove('users'); // Remove only the 'users' key
  }

  static Future setListString({
    required String id,
    required String name,
    required String class_name,
    required String branch_name,
    required String section_name,
    required String section_id,
    required String class_id,
    required String branch_id,
    required String type,
    required String profile,
    required String bloodgroup,
    required String fathername,
    required String mothername,
    required String primarynumber,
    required String secondarynumber,
    required String year_id,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setStringList('users', [
      id,
      name,
      class_name,
      branch_name,
      section_name,
      section_id,
      class_id,
      branch_id,
      profile,
      bloodgroup,
      fathername,
      mothername,
      primarynumber,
      secondarynumber,
      year_id,
      type,
    ]);
    // final notificationManager = FirebaseApi();
    // notificationManager.Auth_Users();
    Get.to(const MainBoard());
  }

  Widget _buildRow(ProfileObject branch) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 20.0, top: 20.0),
          child: CircleAvatar(
            radius: 40.0,
            child:
                isTeacher == true
                    ? Image.asset("images/user.png")
                    : ClipRRect(
                      borderRadius: BorderRadius.circular(80.0),
                      child: Image.network(
                        branch.profile,
                        width: 160,
                        height: 160,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Image.asset(
                            'images/user.png',
                            width: 160,
                            height: 160,
                          );
                        },
                      ),
                    ),
          ),
        ),
        // Add some spacing
        Padding(
          padding: EdgeInsets.only(top: 25.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isTeacher == false ? branch.name : "Teacher",
                style: textStyle,
              ),
              Visibility(
                visible: isTeacher == false ? false : true,
                child: Text("Class Teacher"),
              ),
              Text(
                'Std - ${branch.className}-${branch.sectionName}',
                style: textStyle.copyWith(fontSize: 18),
              ),
            ],
          ),
        ),

        Padding(
          padding: EdgeInsets.only(top: 30.0),
          child: GestureDetector(
            onTap: () {
              if (branch.id != widget.id) {
                logout();
                setListString(
                  id: branch.id.toString(),
                  name: branch.name,
                  class_name: branch.className,
                  branch_name: branch.branchName,
                  section_name: branch.sectionName,
                  section_id: branch.sectionId,
                  class_id: branch.classId,
                  branch_id: branch.branchId,
                  type: branch.type,
                  profile: branch.profile,
                  bloodgroup: branch.bloodGroup,
                  fathername: branch.fatherName,
                  mothername: branch.motherName,
                  primarynumber: branch.primaryNumber,
                  secondarynumber: branch.secondaryNumber,
                  year_id: branch.academic_id.toString(),
                );
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text("Account Switched Successfully"),
                  ),
                );
              }
            },
            child: Container(
              decoration: BoxDecoration(
                color:
                    branch.id == widget.id ? Colors.green : Colors.yellow[900],
                borderRadius: BorderRadius.circular(20),
              ),
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 20),
              child:
                  branch.id == widget.id
                      ? Text('Active', style: TextStyle(color: Colors.white))
                      : Text('Select', style: TextStyle(color: Colors.white)),
            ),
          ),
        ),
        SizedBox(height: 20.0.sp), //
      ],
    );
  }

  void _showBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          height:
              MediaQuery.of(context).size.height *
              0.3, // Adjust the height as needed
          width: MediaQuery.of(context).size.width,
          child: SingleChildScrollView(
            child: Column(
              children:
                  student_data_profile
                      .map((branch) => _buildRow(branch))
                      .toList(),
            ),
          ),
        );
      },
    );
  }

  Widget header() {
    return SizedBox(
      width: double.infinity,
      height: 20.0.hp,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 30.0),
            child: CircleAvatar(
              radius: 40.0,
              child:
                  isTeacher == true
                      ? Image.asset("images/user.png")
                      : ClipRRect(
                        borderRadius: BorderRadius.circular(80.0),
                        child: Image.network(
                          widget.student_profile,
                          width: 160,
                          height: 160,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Image.asset(
                              'images/user.png',
                              width: 160,
                              height: 160,
                            );
                          },
                        ),
                      ),
            ),
          ),
          SizedBox(width: 5.0),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isTeacher == false ? widget.student_name : "Teacher",
                  style: textStyle,
                ),
                Visibility(
                  visible: isTeacher == false ? false : true,
                  child: Text("Class Teacher"),
                ),
                Text(
                  'Std - ${widget.student_class}-${widget.student_section},${widget.student_branch} ',
                  style: textStyle.copyWith(fontSize: 18),
                ),
                if (student_data_profile.length > 1)
                  ElevatedButton.icon(
                    onPressed: () {
                      _showBottomSheet();
                    },
                    // onLongPress: () {},
                    icon: const Icon(Icons.add_circle, color: Colors.white),
                    label: const Text(
                      "Switch Account",
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      //primary: Colors.white,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ProfileObject {
  String id;
  String name;
  String className;
  String branchName;
  String sectionName;
  String sectionId;
  String classId;
  String branchId;
  String type;
  String profile;
  String bloodGroup;
  String fatherName;
  String motherName;
  String primaryNumber;
  String secondaryNumber;
  String academic_id;

  ProfileObject({
    required this.id,
    required this.name,
    required this.className,
    required this.branchName,
    required this.sectionName,
    required this.sectionId,
    required this.classId,
    required this.branchId,
    required this.type,
    required this.profile,
    required this.bloodGroup,
    required this.fatherName,
    required this.motherName,
    required this.primaryNumber,
    required this.secondaryNumber,
    required this.academic_id,
  });
}
