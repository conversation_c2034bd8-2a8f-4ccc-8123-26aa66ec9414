import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/User_Profile_Screen/profileScreen.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:silverleaf/view/otpScreen.dart';
import 'package:silverleaf/navabar.dart';
import 'package:http/http.dart' as http;

// ignore: must_be_immutable
class CustomizedAppBar extends StatefulWidget {
  String screenName;
  VoidCallback back;
  VoidCallback profile;
  int screen_id;

  CustomizedAppBar({
    super.key,
    required this.back,
    required this.profile,
    required this.screenName,
    required this.screen_id,
  });

  @override
  State<CustomizedAppBar> createState() => _CustomizedAppBarState();
  OverlayEntry? getOverlayEntry() {
    // return overlayEntry;
  }
}

class _CustomizedAppBarState extends State<CustomizedAppBar> {
  static const List<String> choices = <String>[
    'FirstItem',
    'SecondItem',
    'ThirdItem',
  ];
  OverlayEntry? overlayEntry;
  int overlay_id = 2;
  List userNames = [];
  bool _isDropdownOpen = false;
  List<String> _dropdownOptions = ['Profile', 'Logout'];

  static Future logout() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.remove('users');

    Get.to(
      LoginScreen(
        title: "Login With OTP",
        lableText: "Mobile Number.",
        buttonTitle: "Get OTP",
        function: () => Get.to(const OtpScreen(otp: '', details: [])),
        otp: '',
        details: [],
      ),
    );
  }

  Delete_Users() async {
    var user;
    var server_key;
    var token;
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames.length > 0) {
      user = storedUserNames[0];
      token = prefs.getString('token');

      final url =
          'https://silverleafms.in/silvar_leaf/api/notification/delete-token';
      final data = {
        'token': token,
        'user_id': user,
        'type': '3',
        'class_id': storedUserNames[6],
        'branch_id': storedUserNames[7],
      };

      final response = await http.post(Uri.parse(url), body: data);

      final result = await json.decode(response.body);

      if (result['status'] != '' && result['status'].toString() == 'success') {
        print(result);
      } else {
        print(result);
      }
    }
  }

  showAlertDialog(BuildContext context) {
    // set up the buttons

    // set up the AlertDialog
    AlertDialog alert = AlertDialog(
      title: Text("Warning"),
      content: Text("Are you sure logout?"),
      actions: [],
    );
    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  void choiceAction(String choice) {
    if (choice == 'Profile') {
      //Get.to(const ProfileScreen());
    } else if (choice == 'Logout') {
      // print(choice);
      Delete_Users();
      logout();
    }
  }

  toggleDropdown() {
    showDialog(
      context: context,
      useSafeArea: true,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Container(
            height: 20.0,
            alignment: Alignment.center,
            child: const Text(
              "Are you sure you want to logout?",
              textAlign: TextAlign.center,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Delete_Users();
                // Handle the OK button press
                logout();
              },
              child: const Text('OK'),
            ),
            TextButton(
              onPressed: () {
                // Handle the Cancel button press
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;

        Get.to(
          ProfileScreen(
            student_profile: userNames[8],
            student_name: userNames[1],
            student_class: userNames[2],
            student_section: userNames[4],
            student_branch: userNames[3],
            student_blood_group: userNames[9],
            student_father_name: userNames[10],
            student_mother_name: userNames[11],
            student_primary_contact_no: userNames[12],
            student_secondary_contact_no: userNames[13],
            id: userNames[0],
          ),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    //this.toggleDropdown();
    return SafeArea(
      child: Visibility(
        visible: widget.screenName == "Home" ? false : true,
        child: Container(
          // color: Colors.amber,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 7.0.sp),
                child: IconButton(
                  onPressed: widget.back,
                  icon: const Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.black,
                    weight: 20,
                  ),
                ),
              ),
              Expanded(child: Text(widget.screenName, style: titlestyle)),
              Padding(
                padding: EdgeInsets.only(right: 7.0.sp),
                child: GestureDetector(
                  onTap: () {
                    //Get.to(const ProfileScreen());
                    // _showImagePopup(context);
                    if (widget.screenName != 'Profile') {
                      getListString();
                    } else if (widget.screenName == "Profile") {
                      print('else');
                      toggleDropdown();
                    }
                  },
                  child:
                      widget.screenName == "Profile"
                          ? IconButton(
                            onPressed: () {
                              if (widget.screenName != 'Profile') {
                                print('if');
                                getListString();
                              } else if (widget.screenName == "Profile") {
                                print('else');
                                toggleDropdown();
                              }
                            },
                            icon: const Icon(
                              Icons.logout ??
                                  Icons
                                      .arrow_back_ios_new, // Fallback to arrow_back_ios_new
                              color: Colors.black,
                              size: 20,
                            ),
                          )
                          : CircleAvatar(
                            backgroundColor: Colors.white,
                            radius: 11.0.sp,
                            backgroundImage: ExactAssetImage(
                              'images/profilevector.png',
                            ),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  var titlestyle = GoogleFonts.poppins(
    color: Colors.black,
    fontWeight: FontWeight.w600,
    fontSize: 16.0.sp,
  );
}
