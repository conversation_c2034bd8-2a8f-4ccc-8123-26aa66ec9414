// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class CalenderOfEvents extends StatefulWidget {
  const CalenderOfEvents({super.key});

  @override
  State<CalenderOfEvents> createState() => _CalenderOfEventsState();
}

class _CalenderOfEventsState extends State<CalenderOfEvents> {
  subback() {
    print('back button clicked');

    //setState(() {
    Get.forceAppUpdate();
    Get.back();
    //});
  }

  var jsonData;
  int type = 0;
  List userNames = [];

  List holidayList = [
    'Govt. Holiday',
    "School Holiday",
    "Govt. Holiday",
    "Govt. Holiday",
  ];
  List holidayFunctions = [
    "Tamil New Year",
    "Teacher Training",
    "Gandhi Jayanthi",
    "Pooja Festivals",
  ];
  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        // fetchFeesData();
      });
    }
  }

  void initState() {
    this.getListString();
    //  this.fetchcalendarData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: CustomizedAppBar(
            back: () {
              subback();
            },
            profile: () {},
            screenName: 'Calendar of Events',
            screen_id: 2,
          ),
        ),
        body: SafeArea(
          child: Container(
            child: Column(
              children: [
                SizedBox(height: 2.0.hp),
                Container(
                  color: Color(0xff7FB85D).withOpacity(.3),
                  height: 5.5.hp,
                  width: double.infinity,
                  child: Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 15.0.sp),
                        child: Row(
                          children: [
                            Icon(Icons.filter_alt_outlined),
                            SizedBox(width: 2.0.wp),
                            Text("Sort by"),
                          ],
                        ),
                      ),
                      Expanded(child: SizedBox()),
                      SizedBox(
                        width: MediaQuery.of(context).size.width / 2,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            buttons(
                              'Holidays',
                              typeEvent == 0 ? appcolor : Colors.grey,
                              () {
                                setState(() {
                                  typeEvent = 0;
                                  type = 1;
                                });
                              },
                            ),
                            SizedBox(width: 2.0.wp),
                            buttons(
                              'Others',
                              typeEvent == 1 ? appcolor : Colors.grey,
                              () {
                                setState(() {
                                  typeEvent = 1;
                                  type = 2;
                                });
                              },
                            ),
                            SizedBox(width: 2.0.wp),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  //  child: typeEvent == 0 ? eventsWidget() : otherEvents(),
                  child: eventsWidget(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<List> fetchcalendarData() async {
    print(type);
    final url =
        'https://silverleafms.in/silvar_leaf/api/calendar/view-calendar/$type/${userNames[7]}/${userNames[14]}';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final responseBody = json.decode(response.body);
      // List<dynamic> result = responseBody['data'];
      this.jsonData = responseBody['data'];
      print(this.jsonData);
      return this.jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Widget otherEvents() {
    return SizedBox(
      child: ListView.builder(
        // separatorBuilder: (context, index) {
        //   return SizedBox(
        //     height: .0.hp,
        //   );
        // },
        padding: EdgeInsets.only(left: 25.0.sp),
        itemCount: 2,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Row(
            // crossAxisAlignment: CrossAxisAlignment.center,
            // mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                // child: ,
                height: 15.0.hp,
                width: 20.0.wp,
                decoration: BoxDecoration(
                  border: Border.all(color: appcolor, width: .5.wp),
                  shape: BoxShape.circle,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "14",
                      style: textStyle.copyWith(
                        color: subappcolor,
                        fontSize: 14.0.sp,
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                    Text(
                      "April",
                      style: textStyle.copyWith(
                        color: subappcolor,
                        fontSize: 10.0.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 5.0.wp),
              Expanded(
                child: Container(
                  // color: Colors.amber,
                  height: 15.0.hp,
                  width: MediaQuery.of(context).size.width / 2,
                  alignment: Alignment.centerLeft,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        holidayList[index],
                        style: textStyle.copyWith(
                          fontSize: 14.0.sp,
                          color: subappcolor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: .5.hp),
                      Text(
                        holidayFunctions[index],
                        style: textStyle.copyWith(fontSize: 12.0.sp),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  String getMonthName(int monthNumber) {
    print(monthNumber);
    if (monthNumber < 1 || monthNumber > 12) {
      throw ArgumentError('Month number should be between 1 and 12');
    }
    List<String> monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    print(monthNames[monthNumber - 1]);
    return monthNames[monthNumber - 1];
    // return monthNumber.toString();
  }

  Widget eventsWidget() {
    return SizedBox(
      child: FutureBuilder<List<dynamic>>(
        future: fetchcalendarData(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            print('if-dta-block');
            return Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            print('error');
            return Center(child: CircularProgressIndicator());
          } else {
            if (snapshot.data!.length == 0) {
              return Center(child: Text('No Data'));
            } else {
              List<dynamic> data = snapshot.data!;

              return ListView.separated(
                separatorBuilder: (context, index) {
                  return SizedBox(height: .0.hp);
                },
                padding: EdgeInsets.only(left: 15.0.sp),
                itemCount: snapshot.data!.length,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  final item = data[index];

                  var month = getMonthName(
                    int.parse(item['event_date'].toString().substring(3, 5)),
                  );

                  print(month);
                  return Container(
                    child: Row(
                      // crossAxisAlignment: CrossAxisAlignment.center,
                      // mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          // child: ,
                          height: 15.0.hp,
                          width: 20.0.wp,
                          decoration: BoxDecoration(
                            border: Border.all(color: appcolor, width: .5.wp),
                            shape: BoxShape.circle,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                // "14",
                                item['event_date'].toString().substring(0, 2),
                                style: textStyle.copyWith(
                                  color: subappcolor,
                                  fontSize: 14.0.sp,
                                  fontWeight: FontWeight.w900,
                                ),
                              ),
                              Text(
                                // "April",
                                month.substring(0, 3),
                                style: textStyle.copyWith(
                                  color: subappcolor,
                                  fontSize: 10.0.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 5.0.wp),
                        Expanded(
                          child: Container(
                            // color: Colors.amber,
                            height: 15.0.hp,
                            width: MediaQuery.of(context).size.width / 2,
                            alignment: Alignment.centerLeft,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  item['event_title'],
                                  //holidayList[index],
                                  style: textStyle.copyWith(
                                    color: subappcolor,
                                    fontSize: 14.0.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                SizedBox(height: .5.hp),
                                Padding(
                                  padding: EdgeInsets.only(right: 15.0),
                                  child: Text(
                                    //holidayFunctions[index],
                                    item['description'].toString(),

                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2, //
                                    style: textStyle.copyWith(
                                      fontSize: 12.0.sp,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            }
          }
        },
      ),
    );
  }

  var typeEvent = 3;
  Widget buttons(title, color, tap) {
    return GestureDetector(
      onTap: tap,
      child: Container(
        height: 3.0.hp,
        width: 20.0.wp,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(3.0.sp),
        ),
        alignment: Alignment.center,
        child: Text(
          title,
          style: textStyle.copyWith(
            color: Colors.white,
            fontSize: 10.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
