// ignore_for_file: avoid_unnecessary_containers

import 'package:flutter/material.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';

class AboutSchool extends StatefulWidget {
  const AboutSchool({super.key});

  @override
  State<AboutSchool> createState() => _AboutSchoolState();
}

class _AboutSchoolState extends State<AboutSchool> {
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 10.0.hp),
          child: CustomizedAppBar(
            back: back,
            profile: () {},
            screenName: 'About School',
            screen_id: 2,
          ),
        ),
        body: SingleChildScrollView(
          child: Container(
            child: Column(
              children: [
                Container(
                  // shape: RoundedRectangleBorder(
                  //     borderRadius: BorderRadius.circular(30.0.sp)),
                  color: Colors.white,
                  child: SizedBox(
                    height: 8.0.hp,
                    width: MediaQuery.of(context).size.width - 10,
                    child: Image.asset("images/logo-2.jpg"),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(20.0.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "Silver leaf academy , spreading the joy of education since 2015",
                        style: textStyle.copyWith(fontSize: 10.0.sp),
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "Our aim of the school is to develop a  child centric approach and nurtures a child’s freedom of choice in a holistic way.",
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "We excel in training children with Montessori education and foster the child's natural curiosity, independence, and love of learning by providing an environment that supports their individual development.",
                        style: textStyle.copyWith(fontSize: 10.0.sp),
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "We excel in training children with Montessori education and foster the child's natural curiosity, independence, and love of learning by providing an environment that supports their individual development.",
                        style: textStyle.copyWith(fontSize: 10.0.sp),
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "We also aim to cultivate not just academic skills but also social, emotional, and practical life skills, preparing children to become self-motivated, competent, and compassionate individuals.",
                        style: textStyle.copyWith(fontSize: 10.0.sp),
                      ),
                      SizedBox(height: 2.0.hp),
                      Container(
                        height: 20.0.hp,
                        //width: MediaQuery.of(context).size.width,
                        // decoration: const BoxDecoration(
                        //     color: Color.fromARGB(255, 231, 231, 231),
                        //     borderRadius:
                        //         BorderRadius.all(Radius.circular(20))),
                        child: Center(
                          child: Image.asset("images/silverleaf img - 1.jpg"),
                        ),
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "Future plan",
                        textAlign: TextAlign.left,
                        style: textStyle.copyWith(
                          fontSize: 15.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "Future plans involve the establishment of a dedicated Career Development Centre to assist students in exploring career paths, internship opportunities, and professional mentorship. This initiative aims to better prepare students for the transition from education to the workforce.",
                        style: textStyle.copyWith(fontSize: 10.0.sp),
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "The school is committed to expanding its inclusive education programs to support students with diverse learning needs. This includes specialized training for teachers, the introduction of adaptive learning resources, and the creation of a more inclusive and accessible physical environment.",
                        style: textStyle.copyWith(fontSize: 10.0.sp),
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "Students will have opportunities to develop leadership skills, participate in entrepreneurial projects, and gain practical experience in problem-solving and decision-making.",
                        style: textStyle.copyWith(fontSize: 10.0.sp),
                      ),
                      SizedBox(height: 2.0.hp),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          children: [
                            Expanded(
                              child: Image.asset(
                                "images/img-4.jpg",
                                fit: BoxFit.cover,
                              ),
                            ),
                            SizedBox(width: 2.0.hp),
                            Expanded(
                              child: Image.asset(
                                "images/img-5.jpg",
                                fit: BoxFit.cover,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "The school aims to enrich its arts and cultural programs by introducing additional extracurricular activities, collaborating with local artists and cultural organizations, and hosting cultural events that celebrate diversity and creativity.",
                        style: textStyle.copyWith(fontSize: 10.0.sp),
                      ),
                      SizedBox(height: 2.0.hp),
                      Text(
                        "Recognizing the importance of student well-being, the school plans to enhance its wellness and mental health support services. This includes the implementation of mindfulness programs, counselling services, and educational initiatives to promote emotional and mental health among students.",
                        style: textStyle.copyWith(fontSize: 10.0.sp),
                      ),
                      SizedBox(height: 2.0.hp),
                      Container(
                        height: 150.0,
                        width: MediaQuery.of(context).size.width,
                        child: Image.asset(
                          "images/silverleaf-img-4.jpg",
                          fit: BoxFit.fitWidth,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
